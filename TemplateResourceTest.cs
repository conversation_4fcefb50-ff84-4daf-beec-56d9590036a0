using System;
using System.IO;
using System.Reflection;

namespace TemplateResourceTest
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== 模板资源验证测试 ===");
            Console.WriteLine();

            try
            {
                // 加载 Reports 程序集
                var reportsAssemblyPath = Path.Combine("MassStorageStableTestTool.Reports", "bin", "Debug", "net8.0-windows", "MassStorageStableTestTool.Reports.dll");

                if (!File.Exists(reportsAssemblyPath))
                {
                    Console.WriteLine($"✗ Reports 程序集不存在: {reportsAssemblyPath}");
                    Console.WriteLine("请先编译 Reports 项目");
                    return;
                }

                Console.WriteLine($"✓ 找到 Reports 程序集: {reportsAssemblyPath}");
                var assembly = Assembly.LoadFrom(reportsAssemblyPath);
                Console.WriteLine($"✓ 程序集加载成功: {assembly.FullName}");
                Console.WriteLine();

                // 列出所有嵌入资源
                var resourceNames = assembly.GetManifestResourceNames();
                Console.WriteLine($"嵌入资源总数: {resourceNames.Length}");
                Console.WriteLine();

                // 检查模板资源
                var formats = new[] { "html", "json", "csv", "text" };

                foreach (var format in formats)
                {
                    var expectedResourceName = $"MassStorageStableTestTool.Reports.Templates.{format}.default.liquid";
                    Console.WriteLine($"检查格式: {format.ToUpper()}");
                    Console.WriteLine($"预期资源名称: {expectedResourceName}");

                    bool found = false;
                    foreach (var resourceName in resourceNames)
                    {
                        if (resourceName.Contains($"Templates.{format}.default.liquid"))
                        {
                            Console.WriteLine($"✓ 找到模板资源: {resourceName}");

                            // 尝试读取资源内容
                            using (var stream = assembly.GetManifestResourceStream(resourceName))
                            {
                                if (stream != null)
                                {
                                    using (var reader = new StreamReader(stream))
                                    {
                                        var content = reader.ReadToEnd();
                                        var lines = content.Split('\n');
                                        Console.WriteLine($"  资源内容行数: {lines.Length}");
                                        Console.WriteLine($"  前3行内容:");
                                        for (int i = 0; i < Math.Min(3, lines.Length); i++)
                                        {
                                            var line = lines[i].Trim();
                                            if (!string.IsNullOrEmpty(line))
                                            {
                                                Console.WriteLine($"    {i + 1}: {line}");
                                            }
                                        }
                                    }
                                }
                                else
                                {
                                    Console.WriteLine($"  ✗ 无法读取资源内容");
                                }
                            }
                            found = true;
                            break;
                        }
                    }

                    if (!found)
                    {
                        Console.WriteLine($"✗ 未找到模板资源");
                        Console.WriteLine("  所有资源名称:");
                        foreach (var resourceName in resourceNames)
                        {
                            if (resourceName.Contains("Templates"))
                            {
                                Console.WriteLine($"    - {resourceName}");
                            }
                        }
                    }

                    Console.WriteLine();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发生错误: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }

            Console.WriteLine("=== 测试完成 ===");
        }
    }
}
