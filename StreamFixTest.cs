using MassStorageStableTestTool.Core.Services;
using MassStorageStableTestTool.Core.Models;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace MassStorageStableTestTool.Debug
{
    /// <summary>
    /// 测试进程流混合操作问题的修复
    /// </summary>
    class StreamFixTest
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("=== 进程流修复测试工具 ===");
            Console.WriteLine("测试 'Cannot mix synchronous and asynchronous operation on process stream' 问题的修复");
            Console.WriteLine();

            // 创建日志记录器
            using var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder
                    .AddConsole(options =>
                    {
                        options.IncludeScopes = true;
                        options.TimestampFormat = "HH:mm:ss.fff ";
                    })
                    .SetMinimumLevel(LogLevel.Debug);
            });

            var logger = loggerFactory.CreateLogger<DiskFormatService>();
            var formatService = new DiskFormatService(logger);

            // 获取目标驱动器
            string targetDrive;
            if (args.Length > 0)
            {
                targetDrive = args[0];
            }
            else
            {
                Console.Write("请输入要测试的驱动器盘符 (例如: J:): ");
                targetDrive = Console.ReadLine()?.Trim() ?? "";
            }

            if (string.IsNullOrEmpty(targetDrive))
            {
                Console.WriteLine("错误：未指定驱动器盘符");
                return;
            }

            try
            {
                Console.WriteLine($"目标驱动器: {targetDrive}");
                Console.WriteLine($"测试时间: {DateTime.Now:HH:mm:ss.fff}");
                Console.WriteLine();

                // 1. 基本检查
                Console.WriteLine("1. 基本驱动器检查");
                try
                {
                    var driveInfo = new DriveInfo(targetDrive);
                    Console.WriteLine($"   ✅ 驱动器存在: {driveInfo.Name}");
                    Console.WriteLine($"   ✅ 驱动器就绪: {driveInfo.IsReady}");
                    Console.WriteLine($"   ✅ 驱动器类型: {driveInfo.DriveType}");
                    
                    if (driveInfo.IsReady)
                    {
                        Console.WriteLine($"   ✅ 文件系统: {driveInfo.DriveFormat}");
                        Console.WriteLine($"   ✅ 卷标: '{driveInfo.VolumeLabel}'");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ❌ 驱动器检查失败: {ex.Message}");
                    return;
                }
                Console.WriteLine();

                // 2. 格式化可行性检查
                Console.WriteLine("2. 格式化可行性检查");
                try
                {
                    var (canFormat, issues) = await formatService.CheckDriveFormatabilityAsync(targetDrive);
                    Console.WriteLine($"   可以格式化: {(canFormat ? "✅ 是" : "❌ 否")}");
                    
                    if (!canFormat)
                    {
                        Console.WriteLine("   问题列表:");
                        foreach (var issue in issues)
                        {
                            Console.WriteLine($"     - {issue}");
                        }
                        
                        Console.WriteLine();
                        Console.WriteLine("⚠️ 由于驱动器不能格式化，将跳过实际格式化测试");
                        Console.WriteLine("但我们可以测试进程创建和流处理逻辑");
                        
                        // 测试进程创建但不实际格式化
                        await TestProcessCreationOnly();
                        return;
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ❌ 可行性检查失败: {ex.Message}");
                    return;
                }
                Console.WriteLine();

                // 3. 确认格式化
                Console.WriteLine("3. 格式化确认");
                Console.WriteLine("⚠️ 警告：即将执行实际格式化！");
                Console.WriteLine("这将删除驱动器上的所有数据！");
                Console.Write("确定要继续吗？(输入 'YES' 确认): ");
                var confirmation = Console.ReadLine()?.Trim();

                if (confirmation != "YES")
                {
                    Console.WriteLine("格式化测试已取消");
                    Console.WriteLine("测试进程创建逻辑...");
                    await TestProcessCreationOnly();
                    return;
                }

                // 4. 执行格式化测试
                Console.WriteLine();
                Console.WriteLine("4. 执行格式化测试");
                Console.WriteLine($"   开始时间: {DateTime.Now:HH:mm:ss.fff}");

                var stopwatch = Stopwatch.StartNew();
                var progressCount = 0;
                
                var progress = new Progress<ProgressEventArgs>(args =>
                {
                    progressCount++;
                    Console.WriteLine($"   [{stopwatch.Elapsed:mm\\:ss\\.fff}] 进度 #{progressCount}: {args.Progress}% - {args.Status}");
                });

                var result = await formatService.FormatDriveAsync(
                    targetDrive,
                    fileSystem: null,
                    volumeLabel: null,
                    quickFormat: true,
                    CancellationToken.None,
                    progress);

                stopwatch.Stop();

                Console.WriteLine();
                Console.WriteLine("5. 测试结果");
                Console.WriteLine($"   完成时间: {DateTime.Now:HH:mm:ss.fff}");
                Console.WriteLine($"   总耗时: {stopwatch.Elapsed}");
                Console.WriteLine($"   进度回调次数: {progressCount}");
                Console.WriteLine($"   格式化结果: {(result.Success ? "✅ 成功" : "❌ 失败")}");

                if (result.Success)
                {
                    Console.WriteLine($"   ✅ 文件系统: {result.FileSystem}");
                    Console.WriteLine($"   ✅ 卷标: '{result.VolumeLabel}'");
                    Console.WriteLine($"   ✅ 格式化耗时: {result.Duration}");
                }
                else
                {
                    Console.WriteLine($"   ❌ 错误信息:");
                    var errorLines = result.ErrorMessage?.Split('\n') ?? new string[0];
                    foreach (var line in errorLines)
                    {
                        if (!string.IsNullOrWhiteSpace(line))
                        {
                            Console.WriteLine($"      {line.Trim()}");
                        }
                    }
                    
                    if (result.Exception != null)
                    {
                        Console.WriteLine($"   ❌ 异常类型: {result.Exception.GetType().Name}");
                        Console.WriteLine($"   ❌ 异常消息: {result.Exception.Message}");
                    }
                }

                Console.WriteLine();
                Console.WriteLine("6. 详细日志");
                foreach (var log in result.Logs)
                {
                    Console.WriteLine($"   📝 {log}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试过程中出现未处理的异常:");
                Console.WriteLine($"   类型: {ex.GetType().Name}");
                Console.WriteLine($"   消息: {ex.Message}");
                Console.WriteLine($"   堆栈: {ex.StackTrace}");
            }

            Console.WriteLine();
            Console.WriteLine("=== 测试完成 ===");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        private static async Task TestProcessCreationOnly()
        {
            Console.WriteLine();
            Console.WriteLine("=== 进程创建测试 ===");
            Console.WriteLine("测试进程流处理逻辑（不执行实际格式化）");

            try
            {
                // 测试一个简单的cmd命令来验证进程流处理
                var processStartInfo = new ProcessStartInfo
                {
                    FileName = "cmd.exe",
                    Arguments = "/c echo 测试输出 && echo 错误输出 >&2 && timeout /t 2 /nobreak > nul",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true,
                    StandardOutputEncoding = System.Text.Encoding.UTF8,
                    StandardErrorEncoding = System.Text.Encoding.UTF8
                };

                using var process = new Process { StartInfo = processStartInfo };
                var output = new System.Text.StringBuilder();
                var error = new System.Text.StringBuilder();
                var outputCompleted = false;
                var errorCompleted = false;

                process.OutputDataReceived += (sender, e) =>
                {
                    if (e.Data != null)
                    {
                        output.AppendLine(e.Data);
                        Console.WriteLine($"   📤 输出: {e.Data}");
                    }
                    else
                    {
                        outputCompleted = true;
                        Console.WriteLine($"   ✅ 输出流完成");
                    }
                };

                process.ErrorDataReceived += (sender, e) =>
                {
                    if (e.Data != null)
                    {
                        error.AppendLine(e.Data);
                        Console.WriteLine($"   📤 错误: {e.Data}");
                    }
                    else
                    {
                        errorCompleted = true;
                        Console.WriteLine($"   ✅ 错误流完成");
                    }
                };

                Console.WriteLine("   🚀 启动测试进程...");
                process.Start();
                
                await Task.Delay(200);
                
                Console.WriteLine("   📡 开始异步读取...");
                process.BeginOutputReadLine();
                process.BeginErrorReadLine();

                Console.WriteLine("   ⏳ 等待进程完成...");
                while (!process.HasExited)
                {
                    await Task.Delay(500);
                    Console.WriteLine($"   ⏳ 进程仍在运行...");
                }

                Console.WriteLine("   ⏳ 等待流读取完成...");
                var waitStart = DateTime.Now;
                while ((!outputCompleted || !errorCompleted) && DateTime.Now - waitStart < TimeSpan.FromSeconds(5))
                {
                    await Task.Delay(100);
                }

                Console.WriteLine($"   ✅ 进程测试完成，退出码: {process.ExitCode}");
                Console.WriteLine($"   📝 输出内容: {output.ToString().Trim()}");
                Console.WriteLine($"   📝 错误内容: {error.ToString().Trim()}");
                Console.WriteLine($"   ✅ 输出流完成: {outputCompleted}");
                Console.WriteLine($"   ✅ 错误流完成: {errorCompleted}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"   ❌ 进程测试失败: {ex.Message}");
            }
        }
    }
}
