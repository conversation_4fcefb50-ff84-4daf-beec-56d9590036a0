using System;
using System.Reflection;

namespace MassStorageStableTestTool.Debug
{
    /// <summary>
    /// 简单的模板测试程序，用于验证嵌入资源是否正确
    /// </summary>
    class SimpleTemplateTest
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== 模板资源测试 ===");
            Console.WriteLine();

            // 加载Reports程序集
            try
            {
                var reportsAssemblyPath = @"MassStorageStableTestTool.Reports\bin\Debug\net8.0-windows\MassStorageStableTestTool.Reports.dll";
                if (!System.IO.File.Exists(reportsAssemblyPath))
                {
                    Console.WriteLine($"❌ 找不到Reports程序集: {reportsAssemblyPath}");
                    Console.WriteLine("请先构建MassStorageStableTestTool.Reports项目");
                    return;
                }

                var assembly = Assembly.LoadFrom(reportsAssemblyPath);
                Console.WriteLine($"✅ 成功加载程序集: {assembly.FullName}");
                Console.WriteLine();

                // 列出所有嵌入资源
                var resourceNames = assembly.GetManifestResourceNames();
                Console.WriteLine($"📁 找到 {resourceNames.Length} 个嵌入资源:");
                foreach (var resourceName in resourceNames)
                {
                    Console.WriteLine($"   - {resourceName}");
                }
                Console.WriteLine();

                // 测试各种格式的默认模板
                string[] formats = { "html", "json", "csv", "text" };
                
                foreach (var format in formats)
                {
                    var templateResourceName = $"MassStorageStableTestTool.Reports.Templates.{format}.default.liquid";
                    Console.WriteLine($"测试 {format.ToUpper()} 模板: {templateResourceName}");
                    
                    using var stream = assembly.GetManifestResourceStream(templateResourceName);
                    if (stream != null)
                    {
                        using var reader = new System.IO.StreamReader(stream);
                        var content = reader.ReadToEnd();
                        Console.WriteLine($"   ✅ 模板加载成功 (长度: {content.Length} 字符)");
                        
                        // 显示前100个字符作为预览
                        var preview = content.Length > 100 ? content.Substring(0, 100) + "..." : content;
                        Console.WriteLine($"   预览: {preview.Replace('\n', ' ').Replace('\r', ' ')}");
                    }
                    else
                    {
                        Console.WriteLine($"   ❌ 模板加载失败");
                    }
                    Console.WriteLine();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试失败: {ex.Message}");
                Console.WriteLine($"详细错误: {ex}");
            }

            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
