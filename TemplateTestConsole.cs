using MassStorageStableTestTool.Reports.Generators;
using MassStorageStableTestTool.Reports.Models;
using MassStorageStableTestTool.Core.Models;
using MassStorageStableTestTool.Core.Enums;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace MassStorageStableTestTool.Debug
{
    /// <summary>
    /// 模板测试控制台程序，用于验证报告模板是否正确加载
    /// </summary>
    class TemplateTestConsole
    {
        static async Task Main(string[] args)
        {
            // 创建日志记录器
            using var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder
                    .AddConsole()
                    .SetMinimumLevel(LogLevel.Debug);
            });

            Console.WriteLine("=== 报告模板测试工具 ===");
            Console.WriteLine();

            // 创建测试数据
            var testSuiteResult = CreateTestData();
            var reportConfig = new ReportConfiguration
            {
                Title = "测试报告模板",
                Author = "测试用户",
                Organization = "测试组织"
            };

            // 测试各种格式的报告生成器
            await TestReportGenerator("HTML", new HtmlReportGenerator(loggerFactory.CreateLogger<HtmlReportGenerator>(), reportConfig), testSuiteResult);
            await TestReportGenerator("JSON", new JsonReportGenerator(loggerFactory.CreateLogger<JsonReportGenerator>(), reportConfig), testSuiteResult);
            await TestReportGenerator("CSV", new CsvReportGenerator(loggerFactory.CreateLogger<CsvReportGenerator>(), reportConfig), testSuiteResult);
            await TestReportGenerator("Text", new TextReportGenerator(loggerFactory.CreateLogger<TextReportGenerator>(), reportConfig), testSuiteResult);

            Console.WriteLine();
            Console.WriteLine("测试完成！按任意键退出...");
            Console.ReadKey();
        }

        static async Task TestReportGenerator(string formatName, dynamic generator, TestSuiteResult testSuiteResult)
        {
            try
            {
                Console.WriteLine($"测试 {formatName} 格式报告生成器...");
                
                // 获取可用模板
                var templates = await generator.GetAvailableTemplatesAsync();
                Console.WriteLine($"  可用模板: {string.Join(", ", templates)}");
                
                // 生成报告
                var report = await generator.GenerateReportAsync(testSuiteResult);
                Console.WriteLine($"  ✅ {formatName} 报告生成成功 (长度: {report.Length} 字符)");
                
                // 保存到文件
                var fileName = $"test_report.{formatName.ToLower()}{generator.GetDefaultFileExtension()}";
                await File.WriteAllTextAsync(fileName, report);
                Console.WriteLine($"  📁 报告已保存到: {fileName}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  ❌ {formatName} 报告生成失败: {ex.Message}");
                Console.WriteLine($"     详细错误: {ex}");
            }
            Console.WriteLine();
        }

        static TestSuiteResult CreateTestData()
        {
            var config = new TestConfiguration
            {
                TargetDrive = "J:",
                SelectedTools = new List<string> { "H2testw", "CrystalDiskMark", "ATTO" },
                FormatBeforeTest = true,
                QuickFormat = true,
                TestTimeoutMinutes = 60,
                OutputDirectory = "./TestOutput"
            };

            var testSuiteResult = new TestSuiteResult
            {
                Configuration = config,
                StartTime = DateTime.Now.AddHours(-2),
                EndTime = DateTime.Now,
                Status = TestStatus.Completed
            };

            // 添加一些测试结果
            testSuiteResult.TestResults.Add(new TestResult
            {
                ToolName = "H2testw",
                StartTime = DateTime.Now.AddHours(-2),
                EndTime = DateTime.Now.AddHours(-1.5),
                Status = TestStatus.Completed,
                Success = true,
                OutputFilePath = "./TestOutput/h2testw_result.txt",
                ExitCode = 0,
                CommandLine = "h2testw.exe /t J:",
                WorkingDirectory = "./Tools/H2testw"
            });

            testSuiteResult.TestResults.Add(new TestResult
            {
                ToolName = "CrystalDiskMark",
                StartTime = DateTime.Now.AddHours(-1.5),
                EndTime = DateTime.Now.AddHours(-1),
                Status = TestStatus.Failed,
                Success = false,
                ErrorMessage = "测试超时",
                OutputFilePath = "./TestOutput/crystaldiskmark_result.txt",
                ExitCode = 1,
                CommandLine = "DiskMark64.exe /t J:",
                WorkingDirectory = "./Tools/CrystalDiskMark"
            });

            testSuiteResult.TestResults.Add(new TestResult
            {
                ToolName = "ATTO",
                StartTime = DateTime.Now.AddHours(-1),
                EndTime = DateTime.Now.AddMinutes(-30),
                Status = TestStatus.Completed,
                Success = true,
                OutputFilePath = "./TestOutput/atto_result.txt",
                ExitCode = 0,
                CommandLine = "ATTO.exe /benchmark J:",
                WorkingDirectory = "./Tools/ATTO"
            });

            // 添加驱动器信息
            testSuiteResult.DriveInfo = new DriveInformation
            {
                Name = "J:",
                Label = "TEST_DRIVE",
                FileSystem = "exFAT",
                DriveType = "Removable",
                TotalSize = 64L * 1024 * 1024 * 1024, // 64GB
                AvailableFreeSpace = 32L * 1024 * 1024 * 1024, // 32GB
                IsReady = true
            };

            return testSuiteResult;
        }
    }
}
