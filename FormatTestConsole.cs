using MassStorageStableTestTool.Core.Services;
using MassStorageStableTestTool.Core.Models;
using Microsoft.Extensions.Logging;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace MassStorageStableTestTool.Debug
{
    /// <summary>
    /// 格式化测试控制台程序，用于调试格式化问题
    /// </summary>
    class FormatTestConsole
    {
        static async Task Main(string[] args)
        {
            // 创建日志记录器
            using var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder
                    .AddConsole()
                    .SetMinimumLevel(LogLevel.Debug); // 设置为Debug级别以查看详细日志
            });

            var logger = loggerFactory.CreateLogger<DiskFormatService>();
            var formatService = new DiskFormatService(logger);

            Console.WriteLine("=== 磁盘格式化测试工具 ===");
            Console.WriteLine("警告：此工具会格式化指定的驱动器，请确保数据已备份！");
            Console.WriteLine();

            // 获取目标驱动器
            string targetDrive;
            if (args.Length > 0)
            {
                targetDrive = args[0];
            }
            else
            {
                Console.Write("请输入要格式化的驱动器盘符 (例如: J:): ");
                targetDrive = Console.ReadLine()?.Trim() ?? "";
            }

            if (string.IsNullOrEmpty(targetDrive))
            {
                Console.WriteLine("错误：未指定驱动器盘符");
                return;
            }

            try
            {
                Console.WriteLine($"目标驱动器: {targetDrive}");
                Console.WriteLine();

                // 1. 检查驱动器是否可以格式化
                Console.WriteLine("1. 检查驱动器是否可以格式化...");
                var (canFormat, issues) = await formatService.CheckDriveFormatabilityAsync(targetDrive);
                
                if (!canFormat)
                {
                    Console.WriteLine($"❌ 驱动器不能格式化:");
                    foreach (var issue in issues)
                    {
                        Console.WriteLine($"   - {issue}");
                    }
                    return;
                }
                
                Console.WriteLine("✅ 驱动器检查通过，可以进行格式化");
                Console.WriteLine();

                // 2. 显示当前驱动器信息
                try
                {
                    var driveInfo = new System.IO.DriveInfo(targetDrive);
                    Console.WriteLine("2. 当前驱动器信息:");
                    Console.WriteLine($"   驱动器类型: {driveInfo.DriveType}");
                    Console.WriteLine($"   文件系统: {driveInfo.DriveFormat}");
                    Console.WriteLine($"   卷标: {driveInfo.VolumeLabel}");
                    Console.WriteLine($"   总容量: {driveInfo.TotalSize / (1024 * 1024 * 1024):F2} GB");
                    Console.WriteLine($"   可用空间: {driveInfo.AvailableFreeSpace / (1024 * 1024 * 1024):F2} GB");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"⚠️ 无法获取驱动器信息: {ex.Message}");
                }
                Console.WriteLine();

                // 3. 确认格式化
                Console.WriteLine("3. 确认格式化操作");
                Console.WriteLine("⚠️ 警告：格式化将删除驱动器上的所有数据！");
                Console.Write("确定要继续吗？(输入 'YES' 确认): ");
                var confirmation = Console.ReadLine()?.Trim();
                
                if (confirmation != "YES")
                {
                    Console.WriteLine("操作已取消");
                    return;
                }

                // 4. 执行格式化
                Console.WriteLine();
                Console.WriteLine("4. 开始格式化...");
                
                var progress = new Progress<ProgressEventArgs>(args =>
                {
                    Console.WriteLine($"   进度: {args.Progress}% - {args.Status}");
                });

                var result = await formatService.FormatDriveAsync(
                    targetDrive,
                    fileSystem: null, // 使用当前文件系统
                    volumeLabel: null, // 使用当前卷标
                    quickFormat: true,
                    CancellationToken.None,
                    progress);

                // 5. 显示结果
                Console.WriteLine();
                Console.WriteLine("5. 格式化结果:");
                
                if (result.Success)
                {
                    Console.WriteLine("✅ 格式化成功！");
                    Console.WriteLine($"   文件系统: {result.FileSystem}");
                    Console.WriteLine($"   卷标: {result.VolumeLabel}");
                    Console.WriteLine($"   耗时: {result.Duration}");
                }
                else
                {
                    Console.WriteLine("❌ 格式化失败！");
                    Console.WriteLine($"   错误信息: {result.ErrorMessage}");
                    
                    if (result.Exception != null)
                    {
                        Console.WriteLine($"   异常详情: {result.Exception}");
                    }
                }

                // 显示详细日志
                if (result.Logs.Count > 0)
                {
                    Console.WriteLine();
                    Console.WriteLine("详细日志:");
                    foreach (var log in result.Logs)
                    {
                        Console.WriteLine($"   {log}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 程序执行出错: {ex}");
            }

            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
