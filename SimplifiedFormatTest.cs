using MassStorageStableTestTool.Core.Services;
using MassStorageStableTestTool.Core.Models;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace MassStorageStableTestTool.Debug
{
    /// <summary>
    /// 测试简化版格式化实现
    /// </summary>
    class SimplifiedFormatTest
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("=== 简化版格式化测试 ===");
            Console.WriteLine("测试新的简化异步实现");
            Console.WriteLine();

            // 创建日志记录器
            using var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder
                    .AddConsole(options =>
                    {
                        options.IncludeScopes = true;
                        options.TimestampFormat = "HH:mm:ss.fff ";
                    })
                    .SetMinimumLevel(LogLevel.Debug);
            });

            var logger = loggerFactory.CreateLogger<DiskFormatService>();
            var formatService = new DiskFormatService(logger);

            // 获取目标驱动器
            string targetDrive;
            if (args.Length > 0)
            {
                targetDrive = args[0];
            }
            else
            {
                Console.Write("请输入要测试的驱动器盘符 (例如: J:): ");
                targetDrive = Console.ReadLine()?.Trim() ?? "";
            }

            if (string.IsNullOrEmpty(targetDrive))
            {
                Console.WriteLine("错误：未指定驱动器盘符");
                return;
            }

            try
            {
                Console.WriteLine($"目标驱动器: {targetDrive}");
                Console.WriteLine($"测试时间: {DateTime.Now:HH:mm:ss.fff}");
                Console.WriteLine();

                // 1. 快速状态检查
                Console.WriteLine("1. 驱动器状态检查");
                try
                {
                    var driveInfo = new DriveInfo(targetDrive);
                    Console.WriteLine($"   驱动器就绪: {driveInfo.IsReady}");
                    Console.WriteLine($"   驱动器类型: {driveInfo.DriveType}");
                    if (driveInfo.IsReady)
                    {
                        Console.WriteLine($"   文件系统: {driveInfo.DriveFormat}");
                        Console.WriteLine($"   卷标: '{driveInfo.VolumeLabel}'");
                        Console.WriteLine($"   大小: {driveInfo.TotalSize / (1024 * 1024 * 1024):F2} GB");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ❌ 状态检查失败: {ex.Message}");
                    return;
                }
                Console.WriteLine();

                // 2. 格式化可行性检查
                Console.WriteLine("2. 格式化可行性检查");
                var (canFormat, issues) = await formatService.CheckDriveFormatabilityAsync(targetDrive);
                Console.WriteLine($"   可以格式化: {(canFormat ? "✅ 是" : "❌ 否")}");
                
                if (!canFormat)
                {
                    Console.WriteLine("   问题:");
                    foreach (var issue in issues)
                    {
                        Console.WriteLine($"     - {issue}");
                    }
                    return;
                }
                Console.WriteLine();

                // 3. 确认格式化
                Console.WriteLine("3. 格式化确认");
                Console.WriteLine("⚠️ 警告：即将执行格式化操作！");
                Console.WriteLine("这将删除驱动器上的所有数据！");
                Console.Write("确定要继续吗？(输入 'YES' 确认): ");
                var confirmation = Console.ReadLine()?.Trim();

                if (confirmation != "YES")
                {
                    Console.WriteLine("操作已取消");
                    return;
                }

                // 4. 执行格式化
                Console.WriteLine();
                Console.WriteLine("4. 开始格式化（简化版本）");
                Console.WriteLine($"   开始时间: {DateTime.Now:HH:mm:ss.fff}");

                var stopwatch = Stopwatch.StartNew();
                var progressReports = new List<string>();
                
                var progress = new Progress<ProgressEventArgs>(args =>
                {
                    var report = $"[{stopwatch.Elapsed:mm\\:ss\\.fff}] {args.Progress}% - {args.Status}";
                    progressReports.Add(report);
                    Console.WriteLine($"   📊 {report}");
                });

                var result = await formatService.FormatDriveAsync(
                    targetDrive,
                    fileSystem: null,
                    volumeLabel: null,
                    quickFormat: true,
                    CancellationToken.None,
                    progress);

                stopwatch.Stop();

                // 5. 结果分析
                Console.WriteLine();
                Console.WriteLine("5. 格式化结果");
                Console.WriteLine($"   完成时间: {DateTime.Now:HH:mm:ss.fff}");
                Console.WriteLine($"   总耗时: {stopwatch.Elapsed}");
                Console.WriteLine($"   进度报告次数: {progressReports.Count}");
                Console.WriteLine($"   结果: {(result.Success ? "✅ 成功" : "❌ 失败")}");

                if (result.Success)
                {
                    Console.WriteLine($"   ✅ 文件系统: {result.FileSystem}");
                    Console.WriteLine($"   ✅ 卷标: '{result.VolumeLabel}'");
                    Console.WriteLine($"   ✅ 格式化耗时: {result.Duration}");
                    
                    // 验证结果
                    Console.WriteLine();
                    Console.WriteLine("6. 验证格式化结果");
                    try
                    {
                        await Task.Delay(1000); // 等待系统刷新
                        var newDriveInfo = new DriveInfo(targetDrive);
                        Console.WriteLine($"   ✅ 驱动器就绪: {newDriveInfo.IsReady}");
                        if (newDriveInfo.IsReady)
                        {
                            Console.WriteLine($"   ✅ 新文件系统: {newDriveInfo.DriveFormat}");
                            Console.WriteLine($"   ✅ 新卷标: '{newDriveInfo.VolumeLabel}'");
                            Console.WriteLine($"   ✅ 可用空间: {newDriveInfo.AvailableFreeSpace / (1024 * 1024 * 1024):F2} GB");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"   ⚠️ 验证时出错: {ex.Message}");
                    }
                }
                else
                {
                    Console.WriteLine($"   ❌ 错误信息:");
                    var errorLines = result.ErrorMessage?.Split('\n') ?? new string[0];
                    foreach (var line in errorLines)
                    {
                        if (!string.IsNullOrWhiteSpace(line))
                        {
                            Console.WriteLine($"      {line.Trim()}");
                        }
                    }
                    
                    if (result.Exception != null)
                    {
                        Console.WriteLine($"   ❌ 异常: {result.Exception.GetType().Name}: {result.Exception.Message}");
                    }
                }

                // 6. 详细日志
                Console.WriteLine();
                Console.WriteLine("7. 操作日志");
                foreach (var log in result.Logs)
                {
                    Console.WriteLine($"   📝 {log}");
                }

                // 7. 进度报告详情
                Console.WriteLine();
                Console.WriteLine("8. 进度报告详情");
                for (int i = 0; i < progressReports.Count; i++)
                {
                    Console.WriteLine($"   #{i + 1}: {progressReports[i]}");
                }

                Console.WriteLine();
                Console.WriteLine("=== 简化版测试总结 ===");
                Console.WriteLine($"✅ 避免了复杂的异步流操作");
                Console.WriteLine($"✅ 使用Task.Run保持异步接口");
                Console.WriteLine($"✅ 简化的进程等待逻辑");
                Console.WriteLine($"✅ 同步读取输出，避免竞态条件");
                Console.WriteLine($"✅ 保持了取消和进度报告功能");
                
                if (result.Success)
                {
                    Console.WriteLine($"🎉 格式化成功！新实现工作正常。");
                }
                else
                {
                    Console.WriteLine($"⚠️ 格式化失败，但错误处理正常。");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 测试过程中出现异常:");
                Console.WriteLine($"   {ex.GetType().Name}: {ex.Message}");
                Console.WriteLine($"   堆栈: {ex.StackTrace}");
            }

            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
