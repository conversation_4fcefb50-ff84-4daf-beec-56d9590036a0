using MassStorageStableTestTool.Core.Services;
using MassStorageStableTestTool.Core.Models;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace MassStorageStableTestTool.Debug
{
    /// <summary>
    /// 格式化调试工具，用于诊断单步执行与直接运行的差异
    /// </summary>
    class FormatDebugTool
    {
        static async Task Main(string[] args)
        {
            // 创建详细日志记录器
            using var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder
                    .AddConsole(options =>
                    {
                        options.IncludeScopes = true;
                        options.TimestampFormat = "yyyy-MM-dd HH:mm:ss.fff ";
                    })
                    .SetMinimumLevel(LogLevel.Trace); // 最详细的日志级别
            });

            var logger = loggerFactory.CreateLogger<DiskFormatService>();
            var formatService = new DiskFormatService(logger);

            Console.WriteLine("=== 格式化调试工具 ===");
            Console.WriteLine("此工具用于诊断单步执行与直接运行的差异问题");
            Console.WriteLine();

            // 获取目标驱动器
            string targetDrive;
            if (args.Length > 0)
            {
                targetDrive = args[0];
            }
            else
            {
                Console.Write("请输入要测试的驱动器盘符 (例如: J:): ");
                targetDrive = Console.ReadLine()?.Trim() ?? "";
            }

            if (string.IsNullOrEmpty(targetDrive))
            {
                Console.WriteLine("错误：未指定驱动器盘符");
                return;
            }

            try
            {
                Console.WriteLine($"目标驱动器: {targetDrive}");
                Console.WriteLine($"当前时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
                Console.WriteLine($"进程ID: {Environment.ProcessId}");
                Console.WriteLine($"线程ID: {Environment.CurrentManagedThreadId}");
                Console.WriteLine();

                // 1. 系统环境检查
                Console.WriteLine("=== 1. 系统环境检查 ===");
                await CheckSystemEnvironment();
                Console.WriteLine();

                // 2. 驱动器状态检查
                Console.WriteLine("=== 2. 驱动器状态检查 ===");
                await CheckDriveStatus(targetDrive, formatService);
                Console.WriteLine();

                // 3. 权限检查
                Console.WriteLine("=== 3. 权限检查 ===");
                CheckPermissions();
                Console.WriteLine();

                // 4. 进程测试
                Console.WriteLine("=== 4. 进程执行测试 ===");
                await TestProcessExecution();
                Console.WriteLine();

                // 5. 格式化可行性检查
                Console.WriteLine("=== 5. 格式化可行性检查 ===");
                var (canFormat, issues) = await formatService.CheckDriveFormatabilityAsync(targetDrive);
                Console.WriteLine($"可以格式化: {canFormat}");
                if (!canFormat)
                {
                    Console.WriteLine("问题:");
                    foreach (var issue in issues)
                    {
                        Console.WriteLine($"  - {issue}");
                    }
                }
                Console.WriteLine();

                // 6. 询问是否执行实际格式化测试
                if (canFormat)
                {
                    Console.WriteLine("=== 6. 格式化测试 ===");
                    Console.WriteLine("⚠️ 警告：接下来将执行实际的格式化操作！");
                    Console.WriteLine("这将删除驱动器上的所有数据！");
                    Console.Write("确定要继续吗？(输入 'YES' 确认): ");
                    var confirmation = Console.ReadLine()?.Trim();

                    if (confirmation == "YES")
                    {
                        await ExecuteFormatTest(targetDrive, formatService);
                    }
                    else
                    {
                        Console.WriteLine("格式化测试已跳过");
                    }
                }

                Console.WriteLine();
                Console.WriteLine("=== 调试完成 ===");
                Console.WriteLine("请检查上述输出中的任何异常或错误信息");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"调试过程中出现异常: {ex}");
            }

            Console.WriteLine();
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }

        private static async Task CheckSystemEnvironment()
        {
            Console.WriteLine($"操作系统: {Environment.OSVersion}");
            Console.WriteLine($"CLR版本: {Environment.Version}");
            Console.WriteLine($"工作目录: {Environment.CurrentDirectory}");
            Console.WriteLine($"用户名: {Environment.UserName}");
            Console.WriteLine($"机器名: {Environment.MachineName}");
            
            // 检查cmd.exe是否可用
            try
            {
                var cmdTest = new ProcessStartInfo
                {
                    FileName = "cmd.exe",
                    Arguments = "/c echo CMD_TEST_OK",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    CreateNoWindow = true
                };

                using var process = Process.Start(cmdTest);
                if (process != null)
                {
                    var output = await process.StandardOutput.ReadToEndAsync();
                    await process.WaitForExitAsync();
                    Console.WriteLine($"CMD测试: {(output.Contains("CMD_TEST_OK") ? "✅ 成功" : "❌ 失败")}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"CMD测试: ❌ 异常 - {ex.Message}");
            }
        }

        private static async Task CheckDriveStatus(string driveLetter, DiskFormatService formatService)
        {
            try
            {
                var driveInfo = new DriveInfo(driveLetter);
                Console.WriteLine($"驱动器存在: {driveInfo != null}");
                Console.WriteLine($"驱动器就绪: {driveInfo.IsReady}");
                
                if (driveInfo.IsReady)
                {
                    Console.WriteLine($"驱动器类型: {driveInfo.DriveType}");
                    Console.WriteLine($"文件系统: {driveInfo.DriveFormat}");
                    Console.WriteLine($"卷标: '{driveInfo.VolumeLabel}'");
                    Console.WriteLine($"总空间: {driveInfo.TotalSize / (1024 * 1024 * 1024):F2} GB");
                    Console.WriteLine($"可用空间: {driveInfo.AvailableFreeSpace / (1024 * 1024 * 1024):F2} GB");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"驱动器状态检查异常: {ex.Message}");
            }
        }

        private static void CheckPermissions()
        {
            try
            {
                var identity = System.Security.Principal.WindowsIdentity.GetCurrent();
                var principal = new System.Security.Principal.WindowsPrincipal(identity);
                var isAdmin = principal.IsInRole(System.Security.Principal.WindowsBuiltInRole.Administrator);
                
                Console.WriteLine($"当前用户: {identity.Name}");
                Console.WriteLine($"管理员权限: {(isAdmin ? "✅ 是" : "❌ 否")}");
                
                if (!isAdmin)
                {
                    Console.WriteLine("⚠️ 警告：格式化操作需要管理员权限");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"权限检查异常: {ex.Message}");
            }
        }

        private static async Task TestProcessExecution()
        {
            try
            {
                // 测试format命令是否可用
                var formatTest = new ProcessStartInfo
                {
                    FileName = "cmd.exe",
                    Arguments = "/c format /?",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };

                using var process = Process.Start(formatTest);
                if (process != null)
                {
                    var output = await process.StandardOutput.ReadToEndAsync();
                    var error = await process.StandardError.ReadToEndAsync();
                    await process.WaitForExitAsync();
                    
                    Console.WriteLine($"Format命令测试: {(process.ExitCode == 0 ? "✅ 成功" : "❌ 失败")}");
                    Console.WriteLine($"退出码: {process.ExitCode}");
                    
                    if (!string.IsNullOrEmpty(error))
                    {
                        Console.WriteLine($"错误输出: {error}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"进程执行测试异常: {ex.Message}");
            }
        }

        private static async Task ExecuteFormatTest(string targetDrive, DiskFormatService formatService)
        {
            var stopwatch = Stopwatch.StartNew();
            
            var progress = new Progress<ProgressEventArgs>(args =>
            {
                Console.WriteLine($"[{stopwatch.Elapsed:mm\\:ss\\.fff}] 进度: {args.Progress}% - {args.Status}");
            });

            Console.WriteLine($"[{stopwatch.Elapsed:mm\\:ss\\.fff}] 开始格式化测试...");
            
            var result = await formatService.FormatDriveAsync(
                targetDrive,
                fileSystem: null, // 使用当前文件系统
                volumeLabel: null, // 使用当前卷标
                quickFormat: true,
                CancellationToken.None,
                progress);

            stopwatch.Stop();
            
            Console.WriteLine($"[{stopwatch.Elapsed:mm\\:ss\\.fff}] 格式化测试完成");
            Console.WriteLine($"结果: {(result.Success ? "✅ 成功" : "❌ 失败")}");
            Console.WriteLine($"总耗时: {result.Duration}");
            
            if (!result.Success)
            {
                Console.WriteLine($"错误信息: {result.ErrorMessage}");
                if (result.Exception != null)
                {
                    Console.WriteLine($"异常详情: {result.Exception}");
                }
            }
            else
            {
                Console.WriteLine($"文件系统: {result.FileSystem}");
                Console.WriteLine($"卷标: {result.VolumeLabel}");
            }
            
            Console.WriteLine("日志记录:");
            foreach (var log in result.Logs)
            {
                Console.WriteLine($"  {log}");
            }
        }
    }
}
