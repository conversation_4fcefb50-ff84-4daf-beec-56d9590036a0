Add-Type -AssemblyName System.Reflection

$assemblyPath = "MassStorageStableTestTool.Reports\bin\Debug\net8.0-windows\MassStorageStableTestTool.Reports.dll"

if (-not (Test-Path $assemblyPath)) {
    Write-Host "Assembly not found: $assemblyPath"
    exit 1
}

Write-Host "=== Template Resource Verification Test ==="
Write-Host ""

try {
    $assembly = [System.Reflection.Assembly]::LoadFrom($assemblyPath)
    Write-Host "Assembly loaded successfully: $($assembly.FullName)"
    Write-Host ""

    $resources = $assembly.GetManifestResourceNames()
    Write-Host "Total embedded resources: $($resources.Length)"
    Write-Host ""

    $formats = @("html", "json", "csv", "text")

    foreach ($format in $formats) {
        Write-Host "Checking format: $($format.ToUpper())"
        $expectedResourceName = "MassStorageStableTestTool.Reports.Templates.$format.default.liquid"
        Write-Host "Expected resource name: $expectedResourceName"

        $found = $false
        foreach ($resourceName in $resources) {
            if ($resourceName -like "*Templates.$format.default.liquid*") {
                Write-Host "Found template resource: $resourceName"

                # Try to read resource content
                $stream = $assembly.GetManifestResourceStream($resourceName)
                if ($stream -ne $null) {
                    $reader = New-Object System.IO.StreamReader($stream)
                    $content = $reader.ReadToEnd()
                    $reader.Close()
                    $stream.Close()

                    $lines = $content -split "`n"
                    Write-Host "  Resource content lines: $($lines.Length)"
                    Write-Host "  First 3 lines:"
                    for ($i = 0; $i -lt [Math]::Min(3, $lines.Length); $i++) {
                        $line = $lines[$i].Trim()
                        if ($line -ne "") {
                            Write-Host "    $($i + 1): $line"
                        }
                    }
                } else {
                    Write-Host "  Cannot read resource content"
                }
                $found = $true
                break
            }
        }

        if (-not $found) {
            Write-Host "Template resource not found"
            Write-Host "  All template-related resource names:"
            foreach ($resourceName in $resources) {
                if ($resourceName -like "*Templates*") {
                    Write-Host "    - $resourceName"
                }
            }
        }

        Write-Host ""
    }
} catch {
    Write-Host "Error occurred: $($_.Exception.Message)"
    Write-Host "Stack trace: $($_.Exception.StackTrace)"
}

Write-Host "=== Test Complete ==="
