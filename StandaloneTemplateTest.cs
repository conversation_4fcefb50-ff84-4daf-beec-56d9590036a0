using System;
using System.IO;

namespace StandaloneTemplateTest
{
    class Program
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== 独立模板验证测试 ===");
            Console.WriteLine();

            // 测试模板路径格式
            var formats = new[] { "html", "json", "csv" };
            
            foreach (var format in formats)
            {
                var templatePath = $"MassStorageStableTestTool.Reports.Templates.{format}.default.liquid";
                Console.WriteLine($"测试格式: {format.ToUpper()}");
                Console.WriteLine($"预期模板路径: {templatePath}");
                
                // 检查模板文件是否存在
                var filePath = Path.Combine("MassStorageStableTestTool.Reports", "Templates", format, "default.liquid");
                if (File.Exists(filePath))
                {
                    Console.WriteLine($"✓ 模板文件存在: {filePath}");
                    
                    // 读取文件内容的前几行
                    var lines = File.ReadAllLines(filePath);
                    Console.WriteLine($"  文件行数: {lines.Length}");
                    Console.WriteLine($"  前3行内容:");
                    for (int i = 0; i < Math.Min(3, lines.Length); i++)
                    {
                        Console.WriteLine($"    {i + 1}: {lines[i]}");
                    }
                }
                else
                {
                    Console.WriteLine($"✗ 模板文件不存在: {filePath}");
                }
                
                Console.WriteLine();
            }

            Console.WriteLine("=== 测试完成 ===");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
