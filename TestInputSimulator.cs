using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using MassStorageStableTestTool.Automation.GUI;
using FlaUI.Core;
using FlaUI.Core.AutomationElements;
using FlaUI.UIA3;

namespace TestInputFix
{
    /// <summary>
    /// 测试 InputSimulator 的输入功能
    /// </summary>
    public class TestInputSimulator
    {
        private readonly ILogger<InputSimulator> _logger;
        private readonly InputSimulator _inputSimulator;

        public TestInputSimulator()
        {
            // 创建简单的控制台日志记录器
            var loggerFactory = LoggerFactory.Create(builder =>
                builder.AddConsole().SetMinimumLevel(LogLevel.Debug));
            _logger = loggerFactory.CreateLogger<InputSimulator>();
            _inputSimulator = new InputSimulator(_logger);
        }

        /// <summary>
        /// 测试文本输入功能
        /// </summary>
        public async Task TestTextInput()
        {
            Console.WriteLine("开始测试文本输入功能...");
            
            try
            {
                // 启动记事本进行测试
                var process = System.Diagnostics.Process.Start("notepad.exe");
                await Task.Delay(2000); // 等待记事本启动

                using var automation = new UIA3Automation();
                var notepadWindow = automation.GetDesktop().FindFirstDescendant(cf => cf.ByName("无标题 - 记事本").Or(cf.ByName("Untitled - Notepad")));
                
                if (notepadWindow == null)
                {
                    Console.WriteLine("未找到记事本窗口");
                    return;
                }

                // 查找文本编辑区域
                var textArea = notepadWindow.FindFirstDescendant(cf => cf.ByControlType(FlaUI.Core.Definitions.ControlType.Edit));
                
                if (textArea == null)
                {
                    Console.WriteLine("未找到文本编辑区域");
                    return;
                }

                Console.WriteLine("找到文本编辑区域，开始测试...");

                // 测试1: 输入初始文本
                Console.WriteLine("测试1: 输入初始文本");
                bool result1 = _inputSimulator.TypeText(textArea, "这是初始文本", clearFirst: false);
                Console.WriteLine($"输入初始文本结果: {result1}");
                await Task.Delay(1000);

                // 测试2: 清除并输入新文本
                Console.WriteLine("测试2: 清除并输入新文本");
                bool result2 = _inputSimulator.TypeText(textArea, "这是新的文本内容", clearFirst: true);
                Console.WriteLine($"清除并输入新文本结果: {result2}");
                await Task.Delay(1000);

                // 测试3: 再次清除并输入不同文本
                Console.WriteLine("测试3: 再次清除并输入不同文本");
                bool result3 = _inputSimulator.TypeText(textArea, "第三次输入的文本", clearFirst: true);
                Console.WriteLine($"第三次输入结果: {result3}");
                await Task.Delay(2000);

                Console.WriteLine("测试完成！请检查记事本中的文本是否正确。");
                Console.WriteLine("按任意键关闭记事本...");
                Console.ReadKey();

                // 关闭记事本
                process?.CloseMainWindow();
                process?.WaitForExit(5000);
                if (!process?.HasExited == true)
                {
                    process?.Kill();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中出现错误: {ex.Message}");
            }
        }

        /// <summary>
        /// 主测试方法
        /// </summary>
        public static async Task Main(string[] args)
        {
            var tester = new TestInputSimulator();
            await tester.TestTextInput();
        }
    }
}
