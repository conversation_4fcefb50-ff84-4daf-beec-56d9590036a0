using System;
using System.Threading;
using System.Threading.Tasks;
using FlaUI.Core.WindowsAPI;
using FlaUI.Core.Input;

namespace SimpleInputTest
{
    /// <summary>
    /// 简单的输入测试程序，验证清除和输入功能
    /// </summary>
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("简单输入测试程序");
            Console.WriteLine("这个程序将测试修复后的输入清除功能");
            Console.WriteLine("请打开记事本，然后按任意键开始测试...");
            Console.ReadKey();

            try
            {
                Console.WriteLine("\n开始测试输入功能...");

                // 测试1: 输入初始文本
                Console.WriteLine("测试1: 输入初始文本 '这是初始文本'");
                TypeText("这是初始文本", false);
                await Task.Delay(2000);

                // 测试2: 清除并输入新文本
                Console.WriteLine("测试2: 清除现有内容并输入新文本 '这是新的文本内容'");
                TypeText("这是新的文本内容", true);
                await Task.Delay(2000);

                // 测试3: 再次清除并输入不同文本
                Console.WriteLine("测试3: 再次清除并输入不同文本 '第三次输入的文本'");
                TypeText("第三次输入的文本", true);
                await Task.Delay(2000);

                Console.WriteLine("\n测试完成！请检查记事本中的文本是否正确。");
                Console.WriteLine("最终应该只显示: '第三次输入的文本'");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中出现错误: {ex.Message}");
            }

            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }

        /// <summary>
        /// 输入文本的简化版本，模拟修复后的逻辑
        /// </summary>
        /// <param name="text">要输入的文本</param>
        /// <param name="clearFirst">是否先清空现有内容</param>
        static void TypeText(string text, bool clearFirst)
        {
            try
            {
                if (clearFirst)
                {
                    Console.WriteLine("  - 执行全选操作 (Ctrl+A)");
                    // 全选并删除现有内容
                    Keyboard.TypeSimultaneously(VirtualKeyShort.CONTROL, VirtualKeyShort.KEY_A);
                    Thread.Sleep(50); // 等待全选操作完成
                    
                    Console.WriteLine("  - 执行删除操作 (Delete)");
                    Keyboard.Type(VirtualKeyShort.DELETE);
                    Thread.Sleep(100); // 等待删除操作完成
                    Console.WriteLine("  - 已清除现有内容");
                }

                if (!string.IsNullOrEmpty(text))
                {
                    Console.WriteLine($"  - 输入文本: '{text}'");
                    Keyboard.Type(text);
                    Thread.Sleep(200);
                }

                Console.WriteLine("  - 输入操作完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  - 输入操作失败: {ex.Message}");
            }
        }
    }
}
