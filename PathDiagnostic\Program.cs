using System;
using System.IO;
using System.Security.Principal;

namespace PathDiagnostic;

class Program
{
    static void Main(string[] args)
    {
        Console.WriteLine("=== 路径诊断工具 ===");
        Console.WriteLine();

        // 检查当前用户权限
        var identity = WindowsIdentity.GetCurrent();
        var principal = new WindowsPrincipal(identity);
        var isAdmin = principal.IsInRole(WindowsBuiltInRole.Administrator);
        
        Console.WriteLine($"当前用户: {Environment.UserName}");
        Console.WriteLine($"是否管理员: {isAdmin}");
        Console.WriteLine($"用户域: {Environment.UserDomainName}");
        Console.WriteLine();

        // 显示关键路径信息
        Console.WriteLine("=== 路径信息 ===");
        Console.WriteLine($"当前工作目录: {Environment.CurrentDirectory}");
        Console.WriteLine($"应用程序基目录: {AppDomain.CurrentDomain.BaseDirectory}");
        Console.WriteLine($"应用程序基目录的上级: {Path.GetDirectoryName(AppDomain.CurrentDomain.BaseDirectory)}");
        Console.WriteLine();

        // 测试h2testw.exe的查找
        var relativePath = @".\third part tools\h2testw.exe";
        Console.WriteLine($"=== 查找文件: {relativePath} ===");

        var basePaths = new[]
        {
            Environment.CurrentDirectory,
            AppDomain.CurrentDomain.BaseDirectory,
            Path.GetDirectoryName(AppDomain.CurrentDomain.BaseDirectory)
        };

        for (int i = 0; i < basePaths.Length; i++)
        {
            var basePath = basePaths[i];
            Console.WriteLine($"基准路径 {i + 1}: {basePath ?? "null"}");
            
            if (!string.IsNullOrEmpty(basePath))
            {
                var testPath = Path.Combine(basePath, relativePath.TrimStart('.', '\\', '/'));
                Console.WriteLine($"  测试路径: {testPath}");
                
                try
                {
                    var fileExists = File.Exists(testPath);
                    Console.WriteLine($"  文件存在: {fileExists}");
                    
                    if (fileExists)
                    {
                        var fileInfo = new FileInfo(testPath);
                        Console.WriteLine($"  文件大小: {fileInfo.Length} bytes");
                        Console.WriteLine($"  创建时间: {fileInfo.CreationTime}");
                        Console.WriteLine($"  修改时间: {fileInfo.LastWriteTime}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"  错误: {ex.Message}");
                }
            }
            Console.WriteLine();
        }

        // 测试默认路径解析
        try
        {
            var defaultPath = Path.GetFullPath(relativePath);
            Console.WriteLine($"默认路径解析: {defaultPath}");
            Console.WriteLine($"默认路径文件存在: {File.Exists(defaultPath)}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"默认路径解析错误: {ex.Message}");
        }

        Console.WriteLine();
        Console.WriteLine("=== 目录内容检查 ===");
        
        // 检查third part tools目录
        var thirdPartyToolsPath = Path.Combine(Environment.CurrentDirectory, "third part tools");
        Console.WriteLine($"检查目录: {thirdPartyToolsPath}");
        
        if (Directory.Exists(thirdPartyToolsPath))
        {
            Console.WriteLine("目录存在，内容:");
            try
            {
                var files = Directory.GetFiles(thirdPartyToolsPath, "*.exe");
                foreach (var file in files)
                {
                    Console.WriteLine($"  - {Path.GetFileName(file)}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"  读取目录错误: {ex.Message}");
            }
        }
        else
        {
            Console.WriteLine("目录不存在");
        }

        Console.WriteLine();
        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }
}
