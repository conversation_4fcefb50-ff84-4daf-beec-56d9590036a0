using MassStorageStableTestTool.Core.Services;
using MassStorageStableTestTool.Core.Models;
using Microsoft.Extensions.Logging;
using System.Diagnostics;

namespace MassStorageStableTestTool.Debug
{
    /// <summary>
    /// 快速格式化测试工具，专门用于测试WaitForExitAsync问题的修复
    /// </summary>
    class QuickFormatTest
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("=== 快速格式化测试工具 ===");
            Console.WriteLine("专门测试 WaitForExitAsync 问题的修复");
            Console.WriteLine();

            // 创建详细日志记录器
            using var loggerFactory = LoggerFactory.Create(builder =>
            {
                builder
                    .AddConsole(options =>
                    {
                        options.IncludeScopes = true;
                        options.TimestampFormat = "HH:mm:ss.fff ";
                    })
                    .SetMinimumLevel(LogLevel.Debug);
            });

            var logger = loggerFactory.CreateLogger<DiskFormatService>();
            var formatService = new DiskFormatService(logger);

            // 获取目标驱动器
            string targetDrive;
            if (args.Length > 0)
            {
                targetDrive = args[0];
            }
            else
            {
                Console.Write("请输入要测试的驱动器盘符 (例如: J:): ");
                targetDrive = Console.ReadLine()?.Trim() ?? "";
            }

            if (string.IsNullOrEmpty(targetDrive))
            {
                Console.WriteLine("错误：未指定驱动器盘符");
                return;
            }

            try
            {
                Console.WriteLine($"目标驱动器: {targetDrive}");
                Console.WriteLine($"测试开始时间: {DateTime.Now:HH:mm:ss.fff}");
                Console.WriteLine();

                // 快速检查
                Console.WriteLine("1. 快速检查驱动器状态...");
                var driveInfo = new DriveInfo(targetDrive);
                Console.WriteLine($"   驱动器就绪: {driveInfo.IsReady}");
                Console.WriteLine($"   驱动器类型: {driveInfo.DriveType}");
                if (driveInfo.IsReady)
                {
                    Console.WriteLine($"   当前文件系统: {driveInfo.DriveFormat}");
                    Console.WriteLine($"   当前卷标: '{driveInfo.VolumeLabel}'");
                }
                Console.WriteLine();

                // 检查可格式化性
                Console.WriteLine("2. 检查格式化可行性...");
                var (canFormat, issues) = await formatService.CheckDriveFormatabilityAsync(targetDrive);
                Console.WriteLine($"   可以格式化: {canFormat}");
                if (!canFormat)
                {
                    Console.WriteLine("   问题:");
                    foreach (var issue in issues)
                    {
                        Console.WriteLine($"     - {issue}");
                    }
                    return;
                }
                Console.WriteLine();

                // 确认格式化
                Console.WriteLine("3. 格式化确认");
                Console.WriteLine("⚠️ 警告：即将执行格式化操作！");
                Console.WriteLine("这将删除驱动器上的所有数据！");
                Console.Write("确定要继续吗？(输入 'YES' 确认): ");
                var confirmation = Console.ReadLine()?.Trim();

                if (confirmation != "YES")
                {
                    Console.WriteLine("操作已取消");
                    return;
                }

                // 执行格式化测试
                Console.WriteLine();
                Console.WriteLine("4. 开始格式化测试...");
                Console.WriteLine($"   开始时间: {DateTime.Now:HH:mm:ss.fff}");

                var stopwatch = Stopwatch.StartNew();
                var progress = new Progress<ProgressEventArgs>(args =>
                {
                    Console.WriteLine($"   [{stopwatch.Elapsed:mm\\:ss\\.fff}] {args.Progress}% - {args.Status}");
                });

                var result = await formatService.FormatDriveAsync(
                    targetDrive,
                    fileSystem: null, // 使用当前文件系统
                    volumeLabel: null, // 使用当前卷标
                    quickFormat: true,
                    CancellationToken.None,
                    progress);

                stopwatch.Stop();

                Console.WriteLine();
                Console.WriteLine("5. 测试结果");
                Console.WriteLine($"   完成时间: {DateTime.Now:HH:mm:ss.fff}");
                Console.WriteLine($"   总耗时: {stopwatch.Elapsed}");
                Console.WriteLine($"   格式化结果: {(result.Success ? "✅ 成功" : "❌ 失败")}");

                if (result.Success)
                {
                    Console.WriteLine($"   文件系统: {result.FileSystem}");
                    Console.WriteLine($"   卷标: '{result.VolumeLabel}'");
                    Console.WriteLine($"   格式化耗时: {result.Duration}");
                }
                else
                {
                    Console.WriteLine($"   错误信息: {result.ErrorMessage}");
                    if (result.Exception != null)
                    {
                        Console.WriteLine($"   异常类型: {result.Exception.GetType().Name}");
                        Console.WriteLine($"   异常消息: {result.Exception.Message}");
                    }
                }

                Console.WriteLine();
                Console.WriteLine("6. 详细日志");
                foreach (var log in result.Logs)
                {
                    Console.WriteLine($"   {log}");
                }

                // 验证结果
                Console.WriteLine();
                Console.WriteLine("7. 验证格式化结果...");
                try
                {
                    await Task.Delay(1000); // 等待系统刷新
                    driveInfo = new DriveInfo(targetDrive);
                    Console.WriteLine($"   驱动器就绪: {driveInfo.IsReady}");
                    if (driveInfo.IsReady)
                    {
                        Console.WriteLine($"   文件系统: {driveInfo.DriveFormat}");
                        Console.WriteLine($"   卷标: '{driveInfo.VolumeLabel}'");
                        Console.WriteLine($"   总空间: {driveInfo.TotalSize / (1024 * 1024 * 1024):F2} GB");
                        Console.WriteLine($"   可用空间: {driveInfo.AvailableFreeSpace / (1024 * 1024 * 1024):F2} GB");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   验证时出错: {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中出现异常: {ex}");
            }

            Console.WriteLine();
            Console.WriteLine("=== 测试完成 ===");
            Console.WriteLine("按任意键退出...");
            Console.ReadKey();
        }
    }
}
